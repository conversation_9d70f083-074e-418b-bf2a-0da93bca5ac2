package com.ss.ifrs.actuarial.conf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * Excel导出优化配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "excel.export.optimization")
public class ExcelExportOptimizationConfig {
    
    private static final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
    
    /**
     * 最大动态列数限制
     */
    private int maxDynamicColumns = 50;
    
    /**
     * 内存使用率阈值（百分比）
     */
    private double memoryThreshold = 75.0;
    
    /**
     * 批次大小基数
     */
    private int baseBatchSize = 1000;
    
    /**
     * 是否启用内存监控
     */
    private boolean enableMemoryMonitoring = true;
    
    /**
     * 是否启用动态列限制
     */
    private boolean enableDynamicColumnLimit = true;
    
    /**
     * 是否启用自动垃圾回收
     */
    private boolean enableAutoGC = true;
    
    /**
     * GC触发的内存使用率阈值
     */
    private double gcThreshold = 80.0;
    
    @PostConstruct
    public void init() {
        log.info("Excel导出优化配置初始化:");
        log.info("  - 最大动态列数: {}", maxDynamicColumns);
        log.info("  - 内存阈值: {}%", memoryThreshold);
        log.info("  - 基础批次大小: {}", baseBatchSize);
        log.info("  - 启用内存监控: {}", enableMemoryMonitoring);
        log.info("  - 启用动态列限制: {}", enableDynamicColumnLimit);
        log.info("  - 启用自动GC: {}", enableAutoGC);
        log.info("  - GC阈值: {}%", gcThreshold);
    }
    
    /**
     * 定期内存监控任务
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void monitorMemoryUsage() {
        if (!enableMemoryMonitoring) {
            return;
        }
        
        try {
            MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
            long heapUsed = heapMemoryUsage.getUsed();
            long heapMax = heapMemoryUsage.getMax();
            double heapUsagePercent = (double) heapUsed / heapMax * 100;
            
            if (heapUsagePercent > memoryThreshold) {
                log.warn("内存使用率过高: {}% (阈值: {}%), 已用: {}MB, 最大: {}MB", 
                        String.format("%.2f", heapUsagePercent),
                        memoryThreshold,
                        heapUsed / 1024 / 1024,
                        heapMax / 1024 / 1024);
                
                // 如果启用自动GC且超过GC阈值，触发垃圾回收
                if (enableAutoGC && heapUsagePercent > gcThreshold) {
                    log.info("触发自动垃圾回收，当前内存使用率: {}%", String.format("%.2f", heapUsagePercent));
                    System.gc();
                    
                    // 等待GC完成后再次检查
                    try {
                        Thread.sleep(1000);
                        MemoryUsage afterGcUsage = memoryMXBean.getHeapMemoryUsage();
                        double afterGcPercent = (double) afterGcUsage.getUsed() / afterGcUsage.getMax() * 100;
                        log.info("GC后内存使用率: {}%", String.format("%.2f", afterGcPercent));
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("内存监控任务执行失败", e);
        }
    }
    
    /**
     * 根据动态列数计算优化的批次大小
     * 
     * @param dynamicColumnCount 动态列数
     * @return 优化的批次大小
     */
    public int calculateOptimizedBatchSize(int dynamicColumnCount) {
        if (dynamicColumnCount <= 0) {
            return baseBatchSize;
        }
        
        // 根据列数动态调整批次大小
        // 列数越多，批次越小，减少内存压力
        int adjustedBatchSize = Math.max(50, baseBatchSize / Math.max(1, dynamicColumnCount / 10));
        
        log.debug("计算批次大小 - 动态列数: {}, 批次大小: {}", dynamicColumnCount, adjustedBatchSize);
        return adjustedBatchSize;
    }
    
    /**
     * 限制动态列数
     * 
     * @param originalColumnCount 原始列数
     * @return 限制后的列数
     */
    public int limitDynamicColumns(int originalColumnCount) {
        if (!enableDynamicColumnLimit) {
            return originalColumnCount;
        }
        
        int limitedCount = Math.min(originalColumnCount, maxDynamicColumns);
        
        if (limitedCount < originalColumnCount) {
            log.warn("动态列数被限制 - 原始: {}, 限制后: {}, 最大允许: {}", 
                    originalColumnCount, limitedCount, maxDynamicColumns);
        }
        
        return limitedCount;
    }
    
    /**
     * 检查当前内存使用情况
     * 
     * @return 内存使用率百分比
     */
    public double getCurrentMemoryUsage() {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        return (double) heapMemoryUsage.getUsed() / heapMemoryUsage.getMax() * 100;
    }
    
    /**
     * 检查是否需要进行内存清理
     * 
     * @return 如果需要清理返回true
     */
    public boolean shouldPerformCleanup() {
        return getCurrentMemoryUsage() > memoryThreshold;
    }
    
    @PreDestroy
    public void destroy() {
        log.info("Excel导出优化配置销毁");
    }
    
    // Getters and Setters
    public int getMaxDynamicColumns() {
        return maxDynamicColumns;
    }
    
    public void setMaxDynamicColumns(int maxDynamicColumns) {
        this.maxDynamicColumns = maxDynamicColumns;
    }
    
    public double getMemoryThreshold() {
        return memoryThreshold;
    }
    
    public void setMemoryThreshold(double memoryThreshold) {
        this.memoryThreshold = memoryThreshold;
    }
    
    public int getBaseBatchSize() {
        return baseBatchSize;
    }
    
    public void setBaseBatchSize(int baseBatchSize) {
        this.baseBatchSize = baseBatchSize;
    }
    
    public boolean isEnableMemoryMonitoring() {
        return enableMemoryMonitoring;
    }
    
    public void setEnableMemoryMonitoring(boolean enableMemoryMonitoring) {
        this.enableMemoryMonitoring = enableMemoryMonitoring;
    }
    
    public boolean isEnableDynamicColumnLimit() {
        return enableDynamicColumnLimit;
    }
    
    public void setEnableDynamicColumnLimit(boolean enableDynamicColumnLimit) {
        this.enableDynamicColumnLimit = enableDynamicColumnLimit;
    }
    
    public boolean isEnableAutoGC() {
        return enableAutoGC;
    }
    
    public void setEnableAutoGC(boolean enableAutoGC) {
        this.enableAutoGC = enableAutoGC;
    }
    
    public double getGcThreshold() {
        return gcThreshold;
    }
    
    public void setGcThreshold(double gcThreshold) {
        this.gcThreshold = gcThreshold;
    }
}
