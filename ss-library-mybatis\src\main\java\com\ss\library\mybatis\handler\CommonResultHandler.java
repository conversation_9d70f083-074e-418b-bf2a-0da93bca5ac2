package com.ss.library.mybatis.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.Lists;
import com.ss.library.utils.DateUtil;
import com.ss.library.utils.FileUtil;
import com.ss.library.utils.ReflectionUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 用于处理excel导出的mybatis流式查询结果处理器
 *
 * <AUTHOR>
 */
@Getter
@Slf4j
public abstract class CommonResultHandler<T> implements ResultHandler<T> {

    private final String XLSX = ".xlsx";

    /**
     * 打印记录的条数设置
     */
    private final int printNumber = 5000;

    /**
     * Writer标识，用于资源管理
     */
    private String writerId;

    /**
     * 单个sheet最大的行数
     */
    private final int maxRowNumber = 1000000;

    private Long totalRowNumber;

    private int maxSheetNo = 1;

    private int sheetNo = 1;

    private final Boolean isLargeFile;

    /**
     * 是否是模板导出
     */
    private boolean isTempExport = false;
    private boolean hasData = false; // 新增有无数据标志位

    /**
     * 模板导出的模板中的bean的别名
     */
    private String tempExportBeanAlias;

    protected final ExcelWriter writer;

    /**
     * 是否使用zip方式导出
     * null:自动判断，超过阈值使用zip格式，没超过则单文件
     * false：不使用zip导出
     * true：使用zip导出
     */
    protected Boolean isExportByZip;

    /**
     * 记录行数计数器
     */
    protected final AtomicInteger currentSheetRowNumber = new AtomicInteger(0);

    /**
     * 导出后的文件路径
     */
    protected final String filePath;

    /**
     * 导出后的文件名称
     */
    protected final String fileName;

    protected WriteSheet sheet;


    /**
     * 表头对应的字段的，在对象中的属性名称
     */
    protected List<String> filedValue;

    protected final List<Object> rowDataList;

    private InputStream templateStream;

    private boolean stopped = false;

    // 新增：初始sheet编号字段
    private final int initialSheetNo;

    /**
     * 创建一个使用对象的字段名称作为表头的resultHandler，数据条数超过阈值则使用zip导出
     *
     * @param filePath 导出文件存放路径，以路径分隔符作为结尾
     * @param fileName 文件名称，不以.xlsx结尾
     * @param clazz    对象
     */
    public CommonResultHandler(String filePath, String fileName, Class<? extends T> clazz) {
        this(filePath, fileName, clazz, null);
    }

    /**
     * 创建一个使用对象的字段名称作为表头的resultHandler，可选是否使用zip方式导出
     *
     * @param filePath      导出文件存放路径，以路径分隔符作为结尾
     * @param fileName      文件名称，不以.xlsx结尾
     * @param clazz         对象
     * @param isExportByZip 是否使用zip格式导出。
     *                      null:自动判断，超过阈值使用zip格式，没超过则单文件;
     *                      false：不使用zip导出;
     *                      true：使用zip导出
     */
    public CommonResultHandler(String filePath, String fileName, Class<? extends T> clazz, Boolean isExportByZip) {
        // 判断文件夹是否存在
        FileUtil.checkAndCreateDir(filePath);
        isLargeFile = false;
        String outPutPath = filePath + fileName + XLSX;
        this.filePath = filePath;
        this.fileName = fileName;
        this.isExportByZip = isExportByZip;
        this.writer = EasyExcel.write(outPutPath, clazz).build();
        rowDataList = new ArrayList<>(1);
        this.initialSheetNo = 1;

        // 创建Writer标识并注册到资源管理器
        this.writerId = ExcelResourceManager.createWriterId(fileName, Thread.currentThread().getName());
        ExcelResourceManager.registerWriter(this.writerId, this.writer);
        this.initSheet();
    }


    /**
     * 创建一个导出自定义表头的resultHandler，数据条数超过阈值则使用zip导出
     *
     * @param filePath   导出文件存放路径，以路径分隔符作为结尾
     * @param fileName   文件名称，不以.xlsx结尾
     * @param headValue  表头的名称
     * @param filedValue 表头对应的字段，在对象中的属性名称
     */
    public CommonResultHandler(String filePath, String fileName, List<String> headValue, List<String> filedValue) {
        this(filePath, fileName, headValue, filedValue, null);
    }

    /**
     * 创建一个导出自定义表头的resultHandler,可选是否使用zip方式导出
     *
     * @param filePath      导出文件存放路径，以路径分隔符作为结尾
     * @param fileName      文件名称，不以.xlsx结尾
     * @param headValue     表头的名称
     * @param filedValue    表头对应的字段，在对象中的属性名称
     * @param isExportByZip 是否使用zip格式导出。
     *                      null:自动判断，超过阈值使用zip格式，没超过则单文件;
     *                      false：不使用zip导出;
     *                      true：使用zip导出
     */
    public CommonResultHandler(String filePath, String fileName, List<String> headValue, List<String> filedValue,
                               Boolean isExportByZip) {
        // 判断文件夹是否存在
        FileUtil.checkAndCreateDir(filePath);
        String outPutPath = filePath + fileName + XLSX;
        isLargeFile = false;
        this.filePath = filePath;
        this.fileName = fileName;
        this.filedValue = filedValue;
        this.isExportByZip = isExportByZip;
        this.writer = EasyExcel.write(outPutPath).head(convertHead(headValue)).build();
        rowDataList = new ArrayList<>(1);
        this.initialSheetNo = 1;
        this.initSheet();
    }


    /**
     * 以模板文件导出
     *
     * @param filePath            导出文件存放路径，以路径分隔符作为结尾
     * @param fileName            文件名称，不以.xlsx结尾
     * @param tempFilePath        模板文件存放路径，包含文件名
     * @param tempExportBeanAlias 模板导出的模板中的bean的别名
     * @param isExportByZip       是否使用zip格式导出
     *                            null:自动判断，超过阈值使用zip格式，没超过则单文件;
     *                            false：不使用zip导出;
     *                            true：使用zip导出
     */
    public CommonResultHandler(String filePath, String fileName, String tempFilePath, String tempExportBeanAlias,
                               Boolean isExportByZip) {
        FileUtil.checkAndCreateDir(filePath);
        String outPutPath = filePath + fileName + XLSX;
        this.filePath = filePath;
        this.fileName = fileName;
        this.isExportByZip = isExportByZip;
        this.isLargeFile = false;
        handleTemplateFile(tempFilePath);
        this.writer = EasyExcel.write(outPutPath).withTemplate(tempFilePath).build();
        this.rowDataList = new ArrayList<>(1);
        this.isTempExport = true;
        this.tempExportBeanAlias = tempExportBeanAlias;
        this.initialSheetNo = 1;
        this.initSheet();
    }


    public CommonResultHandler(String filePath, String fileName, String tempFilePath, String tempExportBeanAlias,
                               Boolean isExportByZip, Long totalCount) {
        FileUtil.checkAndCreateDir(filePath);
        String outPutPath = filePath + fileName + XLSX;
        this.totalRowNumber = totalCount;
        this.filePath = filePath;
        this.fileName = fileName;
        this.isExportByZip = isExportByZip;
        maxSheetNo = Math.toIntExact(totalCount / maxRowNumber);
        maxSheetNo += 1;

        this.isLargeFile = maxSheetNo > 1;

        if (isLargeFile) {
            handleTemplateFile(tempFilePath);
            this.writer = EasyExcel.write(outPutPath).withTemplate(templateStream).build();
        } else {
            this.writer = EasyExcel.write(outPutPath).withTemplate(tempFilePath).build();
        }

        // 创建Writer标识并注册到资源管理器
        this.writerId = ExcelResourceManager.createWriterId(fileName, Thread.currentThread().getName());
        ExcelResourceManager.registerWriter(this.writerId, this.writer);
        this.rowDataList = new ArrayList<>(1);
        this.isTempExport = true;
        this.tempExportBeanAlias = tempExportBeanAlias;
        this.initialSheetNo = 1;
        this.initSheet();
    }

    /**
     * 新增：使用共享ExcelWriter的构造函数（模板导出版本）
     *
     * @param excelWriter 共享的ExcelWriter
     * @param tempFilePath 模板文件存放路径，包含文件名
     * @param tempExportBeanAlias 模板导出的模板中的bean的别名
     * @param isExportByZip 是否使用zip格式导出
     * @param totalCount 总记录数（用于计算sheet数量）
     * @param initialSheetNo 初始sheet编号（从1开始）
     */
    public CommonResultHandler(ExcelWriter excelWriter, String tempFilePath, String tempExportBeanAlias,
                               Boolean isExportByZip, Long totalCount, int initialSheetNo) {
        this.writer = excelWriter;
        this.filePath = null;
        this.fileName = null;
        this.isExportByZip = isExportByZip;

        maxSheetNo = Math.toIntExact(totalCount / maxRowNumber);
        maxSheetNo += 1;

        this.isLargeFile = maxSheetNo > 1;

        if (isLargeFile) {
            handleTemplateFile(tempFilePath);
        }

        // 创建Writer标识并注册到资源管理器（共享Writer情况）
        this.writerId = ExcelResourceManager.createWriterId("shared_writer", Thread.currentThread().getName());
        ExcelResourceManager.registerWriter(this.writerId, this.writer);

        this.rowDataList = new ArrayList<>(1);
        this.isTempExport = true;
        this.tempExportBeanAlias = tempExportBeanAlias;

        // 设置初始sheet编号
        this.initialSheetNo = initialSheetNo;
        this.sheetNo = initialSheetNo;
        this.initSheet();
    }


    private void handleTemplateFile(String tempFilePath) {
        try {
            templateStream = Files.newInputStream(new File(tempFilePath).toPath());
            XSSFWorkbook workbook = new XSSFWorkbook(templateStream);
            // 设置模板的第一个sheet的名称，名称我们使用合同号
            for (int i = 1; i < maxSheetNo; i++) {
                // 剩余的全部复制模板sheet0即可
                workbook.cloneSheet(0, "sheet" + (i + 1));
            }
            // 把workbook写到流里
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            byte[] bytes = baos.toByteArray();
            templateStream = new ByteArrayInputStream(bytes);
        } catch (IOException e) {
            log.error("CommonResultHandler#dealTemplateFile clone template sheet fail", e);
            ExceptionUtils.rethrow(e);
        }
    }

    /**
     * 转换为easyExcel的表头格式
     *
     * @param headValue 传入的表头
     * @return easyExcel的表头格式
     */
    private List<List<String>> convertHead(List<String> headValue) {
        if (CollectionUtils.isEmpty(headValue)) {
            return Collections.emptyList();
        }
        return headValue.stream().map(Lists::newArrayList).collect(Collectors.toList());
    }


    /**
     * 初始化sheet
     */
    public void initSheet() {
        this.sheet = EasyExcel.writerSheet(sheetNo - 1).build();
    }

    /**
     * 判断是否生成新的sheet
     */
    private void createNewSheet() {
        if (!isLargeFile) {
            return;
        }
        if (currentSheetRowNumber.get() >= maxRowNumber) {
            sheetNo += 1;
            this.sheet = EasyExcel.writerSheet(sheetNo - 1).build();
            log.info("--------->>>>CommonResultHandler write to new sheet,sheet name is {}", sheet.getSheetName());
            currentSheetRowNumber.getAndSet(0);
        }
    }

    /**
     * 处理结果
     */
    @Override
    public void handleResult(ResultContext<? extends T> resultContext) {
        // 记录行号
        this.hasData = true;
        currentSheetRowNumber.incrementAndGet();
        T obj = resultContext.getResultObject();
        try {
            rowDataList.add(processing(obj));

            // 达到一定条数或达到sheet最大行数，写入
            if (rowDataList.size() >= 5000 || currentSheetRowNumber.get() >= maxRowNumber) {
                if (isTempExport) {
                    createNewSheet();
                    writer.fill(new FillWrapper(tempExportBeanAlias, rowDataList), sheet);
                } else {
                    writer.write(rowDataList, sheet);
                }
                rowDataList.clear();
            }
            if (ObjectUtils.isNotEmpty(this.totalRowNumber)){
                if (currentSheetRowNumber.get() == this.totalRowNumber && rowDataList.size() > 0){
                    if (isTempExport) {
                        createNewSheet();
                        writer.fill(new FillWrapper(tempExportBeanAlias, rowDataList), sheet);
                    } else {
                        writer.write(rowDataList, sheet);
                    }
                    rowDataList.clear();
                }
            }
            // 换 sheet
            if (currentSheetRowNumber.get() >= maxRowNumber) {
                createNewSheet();
            }

            if (resultContext.getResultCount() % printNumber == 0) {
                log.info("--------->>>>CommonResultHandler write to excel size now is {},excel name is {}",
                        resultContext.getResultCount(), fileName);
            }
        } catch (Exception e) {
            log.error("CommonResultHandler#handleResult error!", e);
            close();
            ExceptionUtils.rethrow(e);

        }

        if (resultContext.isStopped()) {
            close();
            stopped = true;
        }
    }





    private void closeTemplateStream() {
        if (isLargeFile) {
            try {
                templateStream.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void close() {
        try {
            if (!rowDataList.isEmpty()) {
                writer.write(rowDataList, sheet);
                rowDataList.clear();
            }

            // 使用资源管理器安全关闭Writer
            ExcelResourceManager.safeCloseWriter(this.writerId);

        } catch (Exception e) {
            log.error("关闭CommonResultHandler时发生异常: writerId={}", this.writerId, e);
        } finally {
            // 在finally块中确保模板流被关闭
            closeTemplateStream();

            // 显式清理引用，帮助GC回收
            if (rowDataList != null) {
                rowDataList.clear();
            }
            sheet = null;
        }
    }

    /**
     * 创建resultHandler需要重写该方法
     * 获取对象后，可在此处处理对象
     * 如果是自定义表头，需要在此处调用本类的dealDataWithHeader()方法处理对象
     *
     * @param t 从流查询中获取的单个结果
     * @return 转换后的结果
     */
    public abstract Object processing(T t);

    /**
     * 处理自定义表头情况下的对象，从对象中获取对应表头字段拼装
     *
     * @param t 查询的单个结果
     * @return 拼装后的数据
     */
    protected List<String> dealDataWithHeader(T t) {
        return filedValue.stream().map(e -> {
            // 反射获取对应字段值
            Object value = ReflectionUtils.getFieldValue(t, e);
            String result;
            // 如果是时间格式，将其格式化后输出
            if (value instanceof Date) {
                result = DateUtil.dateToStr((Date) value, DateUtil.YToSec);
            } else {
                result = value == null ? "" : value.toString();
            }
            return result;
        }).collect(Collectors.toList());
    }

    // 处理结束时的空数据
    public void finish() {
        try {
            // 如果整个查询没有数据，则主动写入一个空列表
            if (!hasData) {
                if (isTempExport) {
                    createNewSheet();
                    writer.fill(new FillWrapper(tempExportBeanAlias, rowDataList), sheet);
                } else {
                    writer.write(rowDataList, sheet);
                }

                // 确保Writer被正确完成
                if (writer != null) {
                    writer.finish();
                }
            }
        } catch (Exception e) {
            log.error("完成CommonResultHandler时发生异常", e);
        } finally {
            // 在finally块中确保模板流被关闭
            closeTemplateStream();
        }
    }
}
