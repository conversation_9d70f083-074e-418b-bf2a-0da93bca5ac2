package com.ss.ifrs.actuarial.service.impl.puhua;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.ss.library.mybatis.handler.ExcelResourceManager;
import com.ss.library.utils.FileUtil;
import com.ss.platform.util.ExcelExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 优化的ResultHandler，专门处理大量动态列的内存问题
 * 
 * <AUTHOR>
 */
@Slf4j
public class OptimizedResultHandler implements ResultHandler<Map<String, Object>> {
    
    private static final String XLSX = ".xlsx";
    private static final int BATCH_SIZE = 1000; // 减少批次大小，更频繁地释放内存
    private static final int MAX_ROWS_PER_SHEET = 1000000;
    
    private final String filePath;
    private final String fileName;
    private final String tempFilePath;
    private final Boolean isExportByZip;
    private final Long totalRowNumber;
    private final int dynamicColumnCount;
    private final String writerId;
    
    private ExcelWriter writer;
    private WriteSheet sheet;
    private List<Map<String, Object>> rowDataList;
    private AtomicLong currentSheetRowNumber;
    private int sheetNo = 1;
    private boolean hasData = false;
    private InputStream templateStream;
    
    public OptimizedResultHandler(String filePath, String fileName, String tempFilePath, 
                                 String tempExportBeanAlias, Boolean isExportByZip, 
                                 Long totalCount, int dynamicColumnCount) {
        FileUtil.checkAndCreateDir(filePath);
        String outPutPath = filePath + fileName + XLSX;
        
        this.filePath = filePath;
        this.fileName = fileName;
        this.tempFilePath = tempFilePath;
        this.isExportByZip = isExportByZip;
        this.totalRowNumber = totalCount;
        this.dynamicColumnCount = dynamicColumnCount;
        this.currentSheetRowNumber = new AtomicLong(0);
        
        // 根据动态列数量调整批次大小
        int adjustedBatchSize = Math.max(100, BATCH_SIZE / Math.max(1, dynamicColumnCount / 10));
        this.rowDataList = new ArrayList<>(adjustedBatchSize);
        
        try {
            // 处理模板文件
            this.writer = EasyExcel.write(outPutPath).withTemplate(tempFilePath).build();
            this.sheet = EasyExcel.writerSheet(0, "Sheet1").build();
            
            // 注册到资源管理器
            this.writerId = ExcelResourceManager.createWriterId(fileName, Thread.currentThread().getName());
            ExcelResourceManager.registerWriter(this.writerId, this.writer);
            
            log.info("OptimizedResultHandler初始化完成 - 文件: {}, 动态列数: {}, 批次大小: {}", 
                    fileName, dynamicColumnCount, adjustedBatchSize);
            
        } catch (Exception e) {
            log.error("初始化OptimizedResultHandler失败: {}", fileName, e);
            throw new RuntimeException("初始化导出处理器失败", e);
        }
    }
    
    @Override
    public void handleResult(ResultContext<? extends Map<String, Object>> resultContext) {
        this.hasData = true;
        currentSheetRowNumber.incrementAndGet();
        
        try {
            Map<String, Object> data = resultContext.getResultObject();
            
            // 优化：创建新的Map来避免持有原始ResultSet的引用
            Map<String, Object> optimizedData = new java.util.HashMap<>(data.size());
            data.forEach((key, value) -> {
                // 只保留非null值，减少内存占用
                if (value != null) {
                    optimizedData.put(key, value);
                }
            });
            
            rowDataList.add(optimizedData);
            
            // 根据动态列数量动态调整写入频率
            int writeThreshold = Math.max(100, BATCH_SIZE / Math.max(1, dynamicColumnCount / 10));
            
            if (rowDataList.size() >= writeThreshold || currentSheetRowNumber.get() >= MAX_ROWS_PER_SHEET) {
                writeAndClearBatch();
            }
            
            // 检查是否需要换sheet
            if (currentSheetRowNumber.get() >= MAX_ROWS_PER_SHEET) {
                createNewSheet();
            }
            
            // 定期监控内存使用情况
            if (resultContext.getResultCount() % 5000 == 0) {
                log.info("导出进度 - 文件: {}, 已处理: {} 行, 当前批次: {} 行", 
                        fileName, resultContext.getResultCount(), rowDataList.size());
                
                // 如果内存使用率过高，强制写入并清理
                if (com.ss.library.thread.ThreadPoolResourceManager.shouldPerformCleanup()) {
                    writeAndClearBatch();
                    System.gc(); // 建议垃圾回收
                }
            }
            
        } catch (Exception e) {
            log.error("处理导出数据时发生异常: 文件={}, 行号={}", fileName, resultContext.getResultCount(), e);
            close();
            throw new RuntimeException("处理导出数据失败", e);
        }
    }
    
    private void writeAndClearBatch() {
        if (!rowDataList.isEmpty()) {
            try {
                writer.fill(new FillWrapper("df", rowDataList), sheet);
                log.debug("写入批次数据 - 文件: {}, 行数: {}", fileName, rowDataList.size());
            } catch (Exception e) {
                log.error("写入批次数据失败: 文件={}, 行数={}", fileName, rowDataList.size(), e);
                throw new RuntimeException("写入Excel数据失败", e);
            } finally {
                // 清理批次数据
                rowDataList.clear();
                
                // 显式清理，帮助GC
                if (rowDataList.size() == 0) {
                    rowDataList = new ArrayList<>(Math.max(100, BATCH_SIZE / Math.max(1, dynamicColumnCount / 10)));
                }
            }
        }
    }
    
    private void createNewSheet() {
        sheetNo++;
        currentSheetRowNumber.set(0);
        this.sheet = EasyExcel.writerSheet(sheetNo - 1, "Sheet" + sheetNo).build();
        log.info("创建新Sheet - 文件: {}, Sheet编号: {}", fileName, sheetNo);
    }
    
    public void finish() {
        try {
            if (!hasData) {
                // 如果没有数据，写入空列表
                writer.fill(new FillWrapper("df", new ArrayList<>()), sheet);
                log.info("写入空数据 - 文件: {}", fileName);
            } else {
                // 写入剩余数据
                writeAndClearBatch();
            }
            
            if (writer != null) {
                writer.finish();
                log.info("完成Excel写入 - 文件: {}", fileName);
            }
            
        } catch (Exception e) {
            log.error("完成Excel写入时发生异常: 文件={}", fileName, e);
        } finally {
            closeTemplateStream();
        }
    }
    
    public void close() {
        try {
            // 写入剩余数据
            writeAndClearBatch();
            
            // 使用资源管理器安全关闭Writer
            ExcelResourceManager.safeCloseWriter(this.writerId);
            
        } catch (Exception e) {
            log.error("关闭OptimizedResultHandler时发生异常: 文件={}", fileName, e);
        } finally {
            closeTemplateStream();
            
            // 显式清理引用
            if (rowDataList != null) {
                rowDataList.clear();
                rowDataList = null;
            }
            sheet = null;
            writer = null;
            
            log.info("OptimizedResultHandler关闭完成 - 文件: {}", fileName);
        }
    }
    
    private void closeTemplateStream() {
        if (templateStream != null) {
            try {
                templateStream.close();
            } catch (IOException e) {
                log.error("关闭模板流时发生异常: 文件={}", fileName, e);
            }
        }
    }
    
    public ExcelWriter getWriter() {
        return writer;
    }
}
