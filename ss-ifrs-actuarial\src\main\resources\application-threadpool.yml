# 线程池配置优化
# 针对Excel导出内存泄漏问题的线程池参数调优

ThreadPool:
  # 核心线程数 - 减少常驻线程数量
  corePoolSize: 5
  
  # 最大线程数 - 控制最大并发数
  maxPoolSize: 15
  
  # 队列容量 - 减少队列大小，避免任务堆积
  queueCapacity: 50
  
  # 线程空闲时间（秒） - 缩短空闲时间，及时回收线程
  keepAlive: 60

# EasyExcel相关配置
excel:
  export:
    # 单次导出最大行数
    maxRowsPerSheet: 1000000

    # 内存使用阈值（百分比）
    memoryThreshold: 75

    # 是否启用资源监控
    enableResourceMonitoring: true

    # 资源清理间隔（分钟）
    cleanupIntervalMinutes: 5

    # 导出优化配置
    optimization:
      # 最大动态列数限制（关键优化：限制动态列数量）
      maxDynamicColumns: 50

      # 内存使用率阈值（百分比）
      memoryThreshold: 75.0

      # 批次大小基数
      baseBatchSize: 1000

      # 是否启用内存监控
      enableMemoryMonitoring: true

      # 是否启用动态列限制
      enableDynamicColumnLimit: true

      # 是否启用自动垃圾回收
      enableAutoGC: true

      # GC触发的内存使用率阈值
      gcThreshold: 80.0

# JVM参数建议（在启动脚本中配置）
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+PrintGCDetails
# -XX:+PrintGCTimeStamps
# -XX:+HeapDumpOnOutOfMemoryError
# -XX:HeapDumpPath=/path/to/heapdumps/
