# 线程池配置优化
# 针对Excel导出内存泄漏问题的线程池参数调优

ThreadPool:
  # 核心线程数 - 减少常驻线程数量
  corePoolSize: 5
  
  # 最大线程数 - 控制最大并发数
  maxPoolSize: 15
  
  # 队列容量 - 减少队列大小，避免任务堆积
  queueCapacity: 50
  
  # 线程空闲时间（秒） - 缩短空闲时间，及时回收线程
  keepAlive: 60

# EasyExcel相关配置
excel:
  export:
    # 单次导出最大行数
    maxRowsPerSheet: 1000000
    
    # 内存使用阈值（百分比）
    memoryThreshold: 75
    
    # 是否启用资源监控
    enableResourceMonitoring: true
    
    # 资源清理间隔（分钟）
    cleanupIntervalMinutes: 5

# JVM参数建议（在启动脚本中配置）
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+PrintGCDetails
# -XX:+PrintGCTimeStamps
# -XX:+HeapDumpOnOutOfMemoryError
# -XX:HeapDumpPath=/path/to/heapdumps/
