#spring框架配置
spring:
  application:
    name: ss-ifrs-actuarial
  profiles:
    active: dev
  http:
    encoding:
      charset: UTF-8
  servlet:
    multipart:
      enabled: true #启动http上传处理
      max-file-size: 10MB  #设置单个文件大小
      max-request-size: 10MB  #设置最大的请求文件大小
      file-size-threshold: 2MB #当上传到2Mb的时候写入
      location: /tmp/ #当上传到2Mb的时候写入的缓存根目录
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    multipart:
      location:
      max-file-size: 20MB
      max-request-size: 50MB
  cache:
    caffeine:
      spec: maximumSize=500,expireAfterAccess=60s
  main:
    allow-bean-definition-overriding: true
  datasource:
    driverClassName: org.postgresql.Driver
#    url: DMapswKXDBoKDdSki2jjDxDqFU04TjsbnDBGA4SHK6ZEVsoVVZrzcWSwSJ4DWkS0
    url: DMapswKXDBoKDdSki2jjDyZ51gJwq-TqoN9xsPVFP8AsBtfEO80_HAiVhXJ_0-fcW-NUuFyQZ2m0By9V4m8TYLLyFwam3F6BqhH-cLzoyLQjs9DoBL2ue3zrDdkx-Vi_
#    url: DMapswKXDBoKDdSki2jjDyZ51gJwq-TqoN9xsPVFP8DK7d6Yemm3Wu-dP_ctyD1a
    username: AUu1e3unUM9NdgnAO_b27g
    password: M70z-ccJGo5IxRJsKDdjkQ
#    url: jdbc:postgresql://***********:5432/i17cs
#    username: atruser
#    password: GaKMgHlWfX
    hikari:
      pool-name: IFRSHikariCPForATR #连接池名称
      connection-timeout: 30000 #等待连接池分配连接的最大时长，默认:30000ms（30秒）
      maximum-pool-size: 40 #连接池最大连接数（包括空闲和正在使用的连接），默认是10，最大连接数 = 核心数 * 2 + 2
      auto-commit: true #连接池返回的连接的默认自动提交行为, 默认值：true
      idle-timeout: 480000 #空闲连接存活最大时间，默认600000ms（10分钟），当前设置8分钟
      max-lifetime: 540000 #连接对象的最长生命周期，值0表示无限生命周期，默认1800000ms（30分钟），当前设置9分钟
  # 安全认证配置
  security:
    user:
      name: ssplatform # 定义用户名
      password: ssplatform123 # 定义密码


# 系统配置
app:
  config:
    basePath: O:/workspace/coder/ifrs17-hgic/templatedoc
    uploadBasePath: /atr/draw/upload
    fileTemplate: /sourceData/uploadTemplate/
    uploadPath: /dataUpload/
    exportExcelTemplate: /atr/export/excelTemplate/
    exportExcelOutTemplate: /atr/export/excelOutTemplate/
  log:
    isDevelopMode: true
  cluster:
    # 缓存集群名称，单个环境保持一致，不同环境保证不同，不然会窜缓存
    group-name: ${spring.profiles.active}
    # 组网方式：默认multicast（组播协议），可选tcp,aws,eureka
    joinType: tcp
    # tcp协议的成员ip。该配置需要将所有子应用涉及的ip以逗号填入
    tcpIpMembers: 127.0.0.1
    # 是否启用hazelcast管理中心
    mancenterEnabled: true
    # hazelcast管理中心访问地址
    mancenterUrl: http://localhost:8200/hazelcast-mancenter
    # eureka 注册配置
    #eureka-self-registration: true
    # eureka 服务的地址
    #eureka-service-url: ${register.urls:http://${spring.security.user.name}:${spring.security.user.password}@127.0.0.1:7602/eureka/}

#服务器设置
server:
  port: 7608
  #tomcat配置
  tomcat:
    #tomcat最大线程
    max-threads: 500
    #Url 编码
    uri-encoding: UTF-8
    max-http-form-post-size: 100000


#日志
logging:
  level:
    org:
      springframework:
        web: INFO
        transaction: INFO
        data: INFO
        security: INFO
      mybatis: INFO


#eureka服务端
eureka:
  instance:
    preferIpAddress: true
  client:
    registerWithEureka: true
    fetchRegistry: true
    serviceUrl:
      defaultZone: ${register.urls:http://${spring.security.user.name}:${spring.security.user.password}@127.0.0.1:7602/eureka/}

#监视和管理
management:
  health:
    redis:
      enabled: false


#mybatis相关配置
mybatis:
  type-handlers-package: com.ss.ifrs.actuarial.util.typehandler
  mapperLocations: classpath*:mapper/postgres/**/*Dao.xml
  basePackage: com.ss.ifrs**.dao
  executor-type: REUSE
  configuration:
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  custom:
    countCacheTimeOut: 6000 # defualt 60000
    maxCacheSize: 16 # default 32
    enableMybatisAuditing: true
    createByFieldName: creatorId
    createDateFieldName: createTime
    lastModifiedByFieldName: updatorId
    lastModifiedDateFieldName: updateTime
    defaultAuditorCode: 1