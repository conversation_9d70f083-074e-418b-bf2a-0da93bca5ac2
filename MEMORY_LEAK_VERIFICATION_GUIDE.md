# 内存泄漏修复验证指南

## 快速验证步骤

### 1. 配置检查
确保以下配置已生效：

```yaml
# application-threadpool.yml
excel:
  export:
    optimization:
      maxDynamicColumns: 50
      enableDynamicColumnLimit: true
      enableMemoryMonitoring: true
      enableAutoGC: true
      memoryThreshold: 75.0
      gcThreshold: 80.0
```

### 2. 日志级别设置
在 `logback-spring.xml` 中添加：

```xml
<logger name="com.ss.ifrs.actuarial.service.impl.puhua" level="INFO"/>
<logger name="com.ss.ifrs.actuarial.conf.ExcelExportOptimizationConfig" level="INFO"/>
<logger name="com.ss.library.thread.ThreadPoolResourceManager" level="INFO"/>
```

### 3. 验证测试

#### 3.1 单次导出测试
1. 启动应用，记录初始内存使用量
2. 执行一次大数据量导出（建议10万+行）
3. 观察日志中的关键信息：
   ```
   导出配置 - 动态列数: 50 (应该≤50)
   OptimizedResultHandler初始化完成 - 批次大小: XXX
   内存使用监控 - 堆内存使用: XXXMb/XXXMb
   ```
4. 导出完成后，等待2-3分钟，检查内存是否回落

#### 3.2 连续导出测试
1. 连续执行3-5次导出操作
2. 每次导出间隔1分钟
3. 观察内存使用趋势，确认无持续增长

#### 3.3 并发导出测试
1. 同时启动2-3个导出任务
2. 观察内存峰值使用情况
3. 检查是否触发自动GC

### 4. 关键指标监控

#### 4.1 内存使用指标
- **导出前内存**: 应在正常范围内（如1.5G）
- **导出中峰值**: 不应超过配置的最大堆内存的80%
- **导出后内存**: 应在5分钟内回落到接近导出前水平

#### 4.2 动态列数指标
- **实际列数**: 应被限制在50列以内
- **批次大小**: 应根据列数动态调整（列数越多，批次越小）

#### 4.3 GC指标
- **GC触发**: 内存使用率超过80%时应自动触发
- **GC效果**: GC后内存使用率应明显下降

### 5. 问题排查

#### 5.1 如果内存仍然泄漏
1. 检查动态列数是否真的被限制：
   ```bash
   grep "动态列数" logs/ss-ifrs-actuarial.log
   ```

2. 检查是否使用了OptimizedResultHandler：
   ```bash
   grep "OptimizedResultHandler" logs/ss-ifrs-actuarial.log
   ```

3. 检查内存监控是否工作：
   ```bash
   grep "内存使用率" logs/ss-ifrs-actuarial.log
   ```

#### 5.2 如果动态列数未被限制
1. 确认配置文件路径正确
2. 检查Spring配置是否加载：
   ```java
   @Autowired
   private ExcelExportOptimizationConfig config;
   // 在代码中打印配置值
   log.info("最大动态列数配置: {}", config.getMaxDynamicColumns());
   ```

#### 5.3 如果批次大小未调整
1. 检查OptimizedResultHandler是否被正确使用
2. 确认动态列数计算逻辑是否正确

### 6. 性能基准

#### 6.1 预期改进效果
- **内存峰值**: 相比修复前应减少50-70%
- **内存稳定性**: 导出后内存应快速回落
- **并发能力**: 支持更多并发导出任务

#### 6.2 性能对比
| 指标 | 修复前 | 修复后 | 改进幅度 |
|------|--------|--------|----------|
| 单次导出内存峰值 | 3.7G | <2.5G | >30% |
| 动态列数 | 121列 | 50列 | 58% |
| 批次大小 | 5000行 | 200-1000行 | 动态调整 |
| 内存回落时间 | >10分钟 | <5分钟 | >50% |

### 7. 监控命令

#### 7.1 实时内存监控
```bash
# 查看JVM内存使用
jstat -gc <pid> 5s

# 查看堆内存详情
jmap -heap <pid>

# 生成堆转储（如需分析）
jmap -dump:format=b,file=heap.hprof <pid>
```

#### 7.2 日志监控
```bash
# 实时监控导出日志
tail -f logs/ss-ifrs-actuarial.log | grep -E "(导出配置|内存使用|OptimizedResultHandler)"

# 统计动态列数分布
grep "动态列数" logs/ss-ifrs-actuarial.log | awk '{print $NF}' | sort | uniq -c
```

### 8. 应急处理

#### 8.1 如果内存仍然过高
1. 临时降低最大动态列数：
   ```yaml
   maxDynamicColumns: 30  # 进一步降低
   ```

2. 减少批次大小：
   ```yaml
   baseBatchSize: 500  # 降低基础批次大小
   ```

3. 启用更激进的GC：
   ```yaml
   gcThreshold: 70.0  # 降低GC触发阈值
   ```

#### 8.2 紧急重启
如果内存泄漏严重，建议：
1. 停止所有导出任务
2. 重启应用服务
3. 应用修复后的配置
4. 逐步恢复导出功能

## 总结

通过限制动态列数量、优化批次处理和增强内存监控，应该能够有效解决内存从1.5G暴增到3.7G的问题。关键是要确保所有优化措施都正确生效，特别是动态列数的限制。
