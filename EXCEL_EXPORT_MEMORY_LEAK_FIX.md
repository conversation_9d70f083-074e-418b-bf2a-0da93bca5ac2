# Excel导出内存泄漏修复方案 v2.0

## 问题根源分析

### 真正的内存泄漏原因
经过深入分析，发现内存从1.5G暴增到3.7G的根本原因是：

1. **大量动态列查询**：每个导出查询会动态生成最多121个列（CF_MAX_COLUMN_DEV = 121）
2. **MyBatis ResultSet内存占用**：121列 × 大量行数据在内存中同时存在
3. **EasyExcel处理大宽表**：大量动态列的数据在EasyExcel处理过程中占用巨大内存
4. **异步环境下的引用持有**：线程池线程持有这些大对象的引用，无法及时释放

### 内存占用计算
- 假设10万行数据 × 121列 × 平均每个字段50字节 ≈ 600MB
- 加上MyBatis、EasyExcel的中间对象，实际内存占用可能达到2-3倍
- 多个并发导出任务会进一步放大内存占用

### 影响范围
- `AtrBussCashFlowServiceImpl.exportHandlerDDAllocData()` 方法
- 所有使用动态列查询的导出功能
- MyBatis流式查询的内存管理
- EasyExcel大宽表处理

## 核心修复方案

### 1. 限制动态列数量（关键优化）

#### 1.1 动态列数量限制
```java
// 将原来的最多121列限制为50列
List<Integer> limitedDevNoList = icgDevNoList.stream()
    .filter(devNo -> devNo <= ActuarialConstant.Export.CF_MAX_COLUMN_DEV)
    .limit(50) // 关键：限制最多50列
    .collect(Collectors.toList());
```

#### 1.2 配置化管理
```yaml
excel:
  export:
    optimization:
      maxDynamicColumns: 50  # 可配置的最大动态列数
```

### 2. 优化的ResultHandler

#### 2.1 `OptimizedResultHandler`
- 根据动态列数量调整批次大小
- 更频繁的内存释放（批次大小从5000降到1000或更小）
- 优化数据结构，避免持有原始ResultSet引用
- 定期内存监控和强制GC

#### 2.2 动态批次大小计算
```java
// 根据列数动态调整批次大小
int adjustedBatchSize = Math.max(100, BATCH_SIZE / Math.max(1, dynamicColumnCount / 10));
```

### 3. 内存监控和自动清理

#### 3.1 `ExcelExportOptimizationConfig`
- 定期内存监控（每分钟检查一次）
- 自动垃圾回收（内存使用率超过80%时触发）
- 可配置的内存阈值和清理策略

#### 3.2 实时内存监控
```java
// 每处理5000行检查一次内存
if (resultContext.getResultCount() % 5000 == 0) {
    if (shouldPerformCleanup()) {
        writeAndClearBatch();
        System.gc();
    }
}
```

### 3. 配置优化

#### 3.1 线程池参数调优
```yaml
ThreadPool:
  corePoolSize: 5      # 减少核心线程数
  maxPoolSize: 15      # 控制最大线程数
  queueCapacity: 50    # 减少队列大小
  keepAlive: 60        # 缩短空闲时间
```

#### 3.2 JVM参数建议
```bash
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
```

## 使用方法

### 1. 启用配置
在 `application.yml` 中引入线程池配置：
```yaml
spring:
  profiles:
    include: threadpool
```

### 2. 监控内存使用
```java
// 在关键操作前后监控内存
ThreadPoolResourceManager.monitorMemoryUsage("操作名称");

// 检查是否需要清理
if (ThreadPoolResourceManager.shouldPerformCleanup()) {
    ThreadPoolResourceManager.performResourceCleanup("操作名称");
}
```

### 3. 管理 Excel 资源
```java
// 注册 Writer（在 CommonResultHandler 中自动完成）
ExcelResourceManager.registerWriter(writerId, writer);

// 安全关闭 Writer（在 close() 方法中自动完成）
ExcelResourceManager.safeCloseWriter(writerId);
```

## 验证方法

### 1. 内存监控
- **导出前后对比**：观察导出任务执行前后的内存使用情况
- **动态列数检查**：确认动态列数被限制在50列以内
- **批次大小验证**：检查批次大小是否根据列数动态调整

### 2. 关键日志检查
查看以下日志输出：
```
导出配置 - 文件: xxx, 业务类型: DD, 维度: ICG, 动态列数: 50
预计导出行数: 100000, 动态列数: 50
OptimizedResultHandler初始化完成 - 文件: xxx, 动态列数: 50, 批次大小: 200
导出进度 - 文件: xxx, 已处理: 5000 行, 当前批次: 200 行
内存使用率过高: 78.5% (阈值: 75.0%)
触发自动垃圾回收，当前内存使用率: 82.3%
GC后内存使用率: 65.2%
```

### 3. 性能测试场景
- **单次大数据量导出**：测试10万+行数据导出的内存使用
- **并发导出测试**：同时进行多个导出任务
- **连续导出测试**：连续执行多次导出，观察内存是否累积
- **内存峰值测试**：确认内存使用不超过配置阈值

## 注意事项

### 1. 兼容性
- 修改保持了原有API的兼容性
- 新增的资源管理功能是可选的
- 不影响现有功能的正常使用

### 2. 性能影响
- 资源监控会有轻微的性能开销
- 建议在生产环境中适当调整监控频率
- 可通过配置开关控制监控功能

### 3. 故障排查
如果仍然出现内存泄漏：
1. 检查 JVM 参数配置
2. 增加 heap dump 分析
3. 调整线程池参数
4. 考虑分批处理大数据量导出

## 后续优化建议

1. **分页导出**：对于大数据量，考虑实现分页导出机制
2. **缓存优化**：优化数据查询和缓存策略
3. **异步通知**：实现导出完成后的异步通知机制
4. **监控告警**：集成监控系统，实现内存使用告警
