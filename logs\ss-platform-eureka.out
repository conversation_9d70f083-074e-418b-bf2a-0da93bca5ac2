[2m2025-07-30 11:02:19.303[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-30 11:02:19.927[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 11:02:20.036[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-30 11:02:20.319[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-30 11:02:20.387[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 1065 ms
[2m2025-07-30 11:02:20.451[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-30 11:02:20.453[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-30 11:02:20.461[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@6573d2f7
[2m2025-07-30 11:02:21.189[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-30 11:02:21.190[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-30 11:02:21.273[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-30 11:02:21.273[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-30 11:02:21.742[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@117b2cc6, org.springframework.security.web.context.SecurityContextPersistenceFilter@1a717d79, org.springframework.security.web.header.HeaderWriterFilter@4899799b, org.springframework.security.web.authentication.logout.LogoutFilter@4ff66917, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@1b3a9ef4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@13aed42b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22ae905f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6def0632, org.springframework.security.web.session.SessionManagementFilter@15994b0b, org.springframework.security.web.access.ExceptionTranslationFilter@6775c0d1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@42107318]
[2m2025-07-30 11:02:21.749[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-30 11:02:21.749[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-30 11:02:21.847[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-30 11:02:22.414[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-30 11:02:22.507[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-30 11:02:22.540[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-30 11:02:22.540[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-30 11:02:22.551[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753844542550 with initial instances count: 0
[2m2025-07-30 11:02:22.589[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-30 11:02:22.590[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-30 11:02:23.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-30 11:02:23.121[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-30 11:02:23.130[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-30 11:02:23.130[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-30 11:02:23.140[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-30 11:02:23.210[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-30 11:02:23.212[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-30 11:02:23.212[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-30 11:02:23.213[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-30 11:02:23.223[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-30 11:02:23.223[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-30 11:02:23.224[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-30 11:02:23.224[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-30 11:02:23.224[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-30 11:02:23.232[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[      Thread-22][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-30 11:02:23.248[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-30 11:02:23.249[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-30 11:02:23.612[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 5.535 seconds (JVM running for 7.864)
[2m2025-07-30 11:02:25.700[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[6)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-30 11:02:25.703[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[6)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 3 ms
[2m2025-07-30 11:02:59.273[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-30 11:02:59.969[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-30 11:03:05.963[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-30 11:03:06.482[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-30 11:03:10.977[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-30 11:03:11.506[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-30 11:03:13.716[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-30 11:03:14.248[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-30 11:03:23.228[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:04:10.384[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 11:04:10.911[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 11:04:23.242[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:05:23.256[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 11:06:23.270[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:07:23.280[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 11:08:23.289[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 11:08:42.161[0;39m [dev] [31mERROR[0;39m [35m52076[0;39m [2m---[0;39m [2m[get_localhost-0][0;39m [36mc.n.e.cluster.ReplicationTaskProcessor  [0;39m [2m:[0;39m It seems to be a socket read timeout exception, it will retry later. if it continues to happen and some eureka node occupied all the cpu time, you should set property 'eureka.server.peer-node-read-timeout-ms' to a bigger value

com.sun.jersey.api.client.ClientHandlerException: java.net.SocketTimeoutException: Read timed out
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.netflix.eureka.cluster.DynamicGZIPContentEncodingFilter.handle(DynamicGZIPContentEncodingFilter.java:48)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.eureka.transport.JerseyReplicationClient.submitBatchUpdates(JerseyReplicationClient.java:117)
	at com.netflix.eureka.cluster.ReplicationTaskProcessor.process(ReplicationTaskProcessor.java:80)
	at com.netflix.eureka.util.batcher.TaskExecutors$BatchWorkerRunnable.run(TaskExecutors.java:190)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 10 common frames omitted

[2m2025-07-30 11:09:23.289[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:10:23.297[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 11:11:23.306[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:12:23.309[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 11:13:23.322[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:14:23.324[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 11:15:23.329[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 11:16:23.335[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 11:17:23.137[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 11:17:23.339[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 11:18:23.346[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 11:19:23.355[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:20:23.370[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 11:21:23.371[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:22:23.387[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 11:23:23.387[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:24:23.387[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:25:23.387[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:26:23.397[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:27:23.403[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 11:28:23.410[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 11:29:23.413[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 11:30:23.419[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 11:31:23.427[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 11:32:23.150[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 11:32:23.442[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 11:33:23.452[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:34:23.457[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 11:35:23.466[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:36:23.482[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 11:37:23.485[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 11:38:23.499[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:39:23.508[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:40:23.508[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:41:23.511[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 11:42:23.512[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:43:23.521[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:44:23.533[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 11:45:23.543[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 11:46:23.557[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:47:23.160[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 11:47:23.572[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 11:48:23.584[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 11:49:23.587[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 11:50:23.590[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 11:51:23.602[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 11:52:23.608[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 11:53:23.622[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 11:54:23.633[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 11:55:23.639[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 11:56:23.653[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 11:57:23.654[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 11:58:23.662[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 11:59:23.670[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 12:00:23.681[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 12:01:23.682[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 12:02:23.161[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 12:02:23.687[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 12:03:23.696[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:04:23.702[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 12:05:23.705[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:06:23.716[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 12:07:23.720[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 12:08:23.724[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:09:23.729[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 12:10:23.732[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:11:23.746[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 12:12:23.756[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 12:13:23.756[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 12:14:23.760[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:15:23.763[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:16:23.772[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:17:23.174[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 12:17:23.778[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 12:18:23.784[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 12:19:23.793[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:20:23.803[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 12:21:23.812[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:22:23.813[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 12:23:23.820[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 12:24:23.822[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 12:25:23.831[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 12:26:23.834[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:27:23.845[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 12:28:23.854[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:29:23.864[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 12:30:23.879[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 12:31:23.892[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 12:32:23.184[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 12:32:23.895[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:33:23.906[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 12:34:23.914[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:35:23.922[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 12:36:23.925[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 12:37:23.936[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 12:38:23.938[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 12:39:23.939[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 12:40:23.947[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 12:41:23.951[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 12:42:23.955[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 12:43:23.970[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 12:44:23.973[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 12:45:23.978[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 12:46:23.991[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 12:47:23.192[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 12:47:23.998[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 12:48:24.012[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 12:49:24.013[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 12:50:24.021[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:51:24.025[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 12:52:24.030[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 12:53:24.030[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 12:54:24.036[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 12:55:24.045[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 12:56:24.052[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 12:57:24.067[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 12:58:24.070[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 12:59:24.075[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:00:24.087[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 13:01:24.093[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:02:23.202[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 13:02:24.097[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 13:03:24.105[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 13:04:24.113[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 13:05:24.127[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 13:06:26.097[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 13:07:26.104[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 13:08:26.110[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 13:09:26.115[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 13:10:26.130[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 13:11:26.133[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:12:26.144[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 13:13:26.153[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 13:14:26.154[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 13:15:26.157[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 13:16:26.165[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 13:17:25.157[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 13:17:26.169[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:18:26.181[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 13:19:26.186[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 13:20:26.193[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 13:21:26.198[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 13:22:26.203[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:23:26.217[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 13:24:26.231[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 13:25:26.234[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 13:26:26.246[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 13:27:26.261[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 13:28:26.266[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:29:26.279[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 13:30:26.282[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:31:26.285[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:32:25.172[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 13:32:26.298[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 13:33:26.310[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 13:34:26.312[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 13:35:26.322[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 13:36:26.326[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:37:26.327[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 13:38:26.332[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:39:26.346[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 13:40:26.348[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 13:41:26.353[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:42:26.358[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:43:26.366[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 13:44:26.372[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 13:45:26.379[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 13:46:26.383[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 13:47:25.181[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 13:47:26.386[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:48:26.398[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 13:49:26.399[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 13:50:26.405[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:51:26.412[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 13:52:26.416[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 13:53:26.419[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 13:54:26.424[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 13:55:26.431[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 13:56:26.431[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 13:57:26.432[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 13:58:26.444[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 13:59:26.459[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:00:26.474[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:01:26.480[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 14:02:25.183[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 14:02:26.494[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 14:03:26.497[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 14:04:26.510[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 14:05:26.512[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 14:06:26.524[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 14:07:26.537[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 14:08:26.552[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:09:26.554[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 14:10:26.565[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 14:11:26.571[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 14:12:26.576[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 14:13:26.583[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 14:14:26.593[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 14:15:26.595[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 14:16:26.606[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 14:17:25.189[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 14:17:26.615[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 14:18:26.617[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 14:19:26.622[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 14:20:26.628[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 14:21:26.642[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 14:22:26.653[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 14:23:26.666[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 14:24:26.677[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 14:25:26.680[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 14:26:26.691[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 14:27:26.698[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 14:28:26.704[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 14:29:26.713[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 14:30:26.714[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 14:31:26.715[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 14:32:25.192[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 14:32:26.727[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 14:33:26.739[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 14:34:26.742[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 14:35:26.751[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 14:36:26.766[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:37:26.771[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 14:38:26.779[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 14:39:26.780[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 14:40:26.782[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 14:41:26.788[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 14:42:26.803[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:43:26.813[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 14:44:26.822[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 14:45:26.828[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 14:46:26.837[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 14:47:25.193[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 14:47:26.842[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 14:48:26.844[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 14:49:26.850[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 14:50:26.864[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 14:51:26.864[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 14:52:26.879[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:53:26.891[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 14:54:26.905[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 14:55:26.915[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 14:56:26.924[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 14:57:26.935[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 14:58:26.948[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 14:59:26.962[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 15:00:26.968[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 15:01:26.974[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 15:02:25.194[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 15:02:26.979[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 15:03:26.994[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 15:04:26.998[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 15:05:26.999[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 15:06:27.011[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 15:07:27.022[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 15:08:27.034[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 15:09:27.042[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 15:10:27.054[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 15:11:27.064[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 15:12:27.071[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 15:13:27.078[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 15:14:27.085[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 15:15:27.092[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 15:16:27.101[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 15:17:25.208[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 15:17:27.114[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 15:18:27.122[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 15:19:27.125[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 15:20:27.128[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 15:21:27.133[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 15:22:27.135[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 15:23:27.144[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 15:24:27.159[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 15:24:57.714[0;39m [dev] [31mERROR[0;39m [35m52076[0;39m [2m---[0;39m [2m[get_localhost-6][0;39m [36mc.n.e.cluster.ReplicationTaskProcessor  [0;39m [2m:[0;39m It seems to be a socket read timeout exception, it will retry later. if it continues to happen and some eureka node occupied all the cpu time, you should set property 'eureka.server.peer-node-read-timeout-ms' to a bigger value

com.sun.jersey.api.client.ClientHandlerException: java.net.SocketTimeoutException: Read timed out
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.netflix.eureka.cluster.DynamicGZIPContentEncodingFilter.handle(DynamicGZIPContentEncodingFilter.java:48)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.eureka.transport.JerseyReplicationClient.submitBatchUpdates(JerseyReplicationClient.java:117)
	at com.netflix.eureka.cluster.ReplicationTaskProcessor.process(ReplicationTaskProcessor.java:80)
	at com.netflix.eureka.util.batcher.TaskExecutors$BatchWorkerRunnable.run(TaskExecutors.java:190)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 10 common frames omitted

[2m2025-07-30 15:25:27.174[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 15:26:27.182[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 15:27:27.187[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 15:28:27.194[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 15:29:27.199[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 15:30:27.200[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 15:31:27.213[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 15:32:25.213[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 15:32:27.228[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 15:33:27.241[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 15:34:27.242[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 15:35:27.243[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 15:36:27.248[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 15:37:27.249[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 15:38:27.257[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 15:39:27.268[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 15:40:27.271[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 15:41:27.272[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 15:42:27.282[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 15:43:27.288[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 15:44:27.294[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 15:45:27.308[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 15:46:27.312[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 15:47:25.221[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 15:47:27.323[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 15:48:27.335[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 15:49:27.337[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 15:50:27.337[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 15:51:27.349[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 15:52:27.353[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 15:53:27.358[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 15:54:27.370[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 15:55:27.376[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 15:56:27.379[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 15:57:27.384[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 15:58:27.389[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 15:59:27.397[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 16:00:27.412[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 16:01:27.422[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:02:25.229[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 16:02:27.430[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 16:03:27.441[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 16:04:27.445[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 16:05:27.457[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 16:06:27.471[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 16:07:27.482[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 16:08:27.490[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 16:09:27.502[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 16:10:27.507[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 16:11:27.518[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 16:12:27.521[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 16:13:27.529[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 16:14:27.539[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:15:27.551[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 16:16:27.551[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 16:17:25.241[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 16:17:27.558[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 16:18:27.560[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 16:19:27.567[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 16:20:27.581[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 16:21:27.594[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 16:22:27.604[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:23:27.615[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 16:24:27.620[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 16:25:27.634[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 16:26:27.644[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 16:27:27.656[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:28:27.657[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 16:29:27.672[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 16:30:27.678[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 16:31:27.680[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 16:32:25.246[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 16:32:27.694[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 16:33:27.695[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 16:34:27.695[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 16:35:27.703[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 16:36:27.707[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 16:37:27.707[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 16:38:27.716[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 16:39:27.727[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 16:40:27.741[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 16:41:27.754[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 16:42:27.765[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 16:43:27.771[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 16:44:27.776[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 16:45:27.780[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 16:46:27.785[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 16:47:25.260[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 16:47:27.790[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 16:48:27.791[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 16:49:27.800[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:50:27.806[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 16:51:27.812[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 16:52:27.815[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 16:53:27.824[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:54:27.833[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 16:55:27.851[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 18ms
[2m2025-07-30 16:56:27.855[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 16:57:27.865[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 16:58:27.877[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 16:59:27.889[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 17:00:27.892[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 17:01:27.897[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:02:25.271[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 17:02:27.910[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 17:03:27.919[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 17:04:27.932[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 17:05:27.942[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 17:05:47.595[0;39m [dev] [31mERROR[0;39m [35m52076[0;39m [2m---[0;39m [2m[et_localhost-13][0;39m [36mc.n.e.cluster.ReplicationTaskProcessor  [0;39m [2m:[0;39m It seems to be a socket read timeout exception, it will retry later. if it continues to happen and some eureka node occupied all the cpu time, you should set property 'eureka.server.peer-node-read-timeout-ms' to a bigger value

com.sun.jersey.api.client.ClientHandlerException: java.net.SocketTimeoutException: Read timed out
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.netflix.eureka.cluster.DynamicGZIPContentEncodingFilter.handle(DynamicGZIPContentEncodingFilter.java:48)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.eureka.transport.JerseyReplicationClient.submitBatchUpdates(JerseyReplicationClient.java:117)
	at com.netflix.eureka.cluster.ReplicationTaskProcessor.process(ReplicationTaskProcessor.java:80)
	at com.netflix.eureka.util.batcher.TaskExecutors$BatchWorkerRunnable.run(TaskExecutors.java:190)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 10 common frames omitted

[2m2025-07-30 17:06:27.956[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 17:07:27.967[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 17:08:27.972[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:09:27.977[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 17:10:27.991[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 17:11:28.000[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 17:12:28.002[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 17:12:33.277[0;39m [dev] [31mERROR[0;39m [35m52076[0;39m [2m---[0;39m [2m[et_localhost-12][0;39m [36mc.n.e.cluster.ReplicationTaskProcessor  [0;39m [2m:[0;39m It seems to be a socket read timeout exception, it will retry later. if it continues to happen and some eureka node occupied all the cpu time, you should set property 'eureka.server.peer-node-read-timeout-ms' to a bigger value

com.sun.jersey.api.client.ClientHandlerException: java.net.SocketTimeoutException: Read timed out
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.netflix.eureka.cluster.DynamicGZIPContentEncodingFilter.handle(DynamicGZIPContentEncodingFilter.java:48)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.eureka.transport.JerseyReplicationClient.submitBatchUpdates(JerseyReplicationClient.java:117)
	at com.netflix.eureka.cluster.ReplicationTaskProcessor.process(ReplicationTaskProcessor.java:80)
	at com.netflix.eureka.util.batcher.TaskExecutors$BatchWorkerRunnable.run(TaskExecutors.java:190)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 10 common frames omitted

[2m2025-07-30 17:13:28.011[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 17:14:28.012[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 17:15:28.028[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 17:16:28.043[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 17:16:57.866[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-30 17:16:58.388[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=true)
[2m2025-07-30 17:17:02.267[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-30 17:17:02.807[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: cancel failed because Lease is not registered for: SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-30 17:17:02.807[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.eureka.resources.InstanceResource   [0;39m [2m:[0;39m Not Found (Cancel): SS-IFRS-ACTUARIAL - DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-30 17:17:02.808[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[et_localhost-16][0;39m [36mc.n.eureka.cluster.ReplicationTask      [0;39m [2m:[0;39m The replication of task SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608:Cancel@localhost failed with response code 404
[2m2025-07-30 17:17:02.808[0;39m [dev] [33m WARN[0;39m [35m52076[0;39m [2m---[0;39m [2m[et_localhost-16][0;39m [36mc.netflix.eureka.cluster.PeerEurekaNode [0;39m [2m:[0;39m SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608:Cancel@localhost: missing entry.
[2m2025-07-30 17:17:25.284[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 17:17:28.054[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 17:18:28.058[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 17:19:28.063[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 17:19:40.900[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 17:19:41.427[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 17:20:13.846[0;39m [dev] [32m INFO[0;39m [35m52076[0;39m [2m---[0;39m [2m[extShutdownHook][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application SS-PLATFORM-EUREKA with eureka with status DOWN
[2m2025-07-30 17:20:37.324[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m The following profiles are active: dev
[2m2025-07-30 17:20:37.743[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 17:20:37.833[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=ff1a2d2c-d19d-3931-a591-81b4348e9e38
[2m2025-07-30 17:20:38.045[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 7602 (http)
[2m2025-07-30 17:20:38.087[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 753 ms
[2m2025-07-30 17:20:38.125[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-30 17:20:38.125[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-30 17:20:38.130[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.netflix.config.DynamicPropertyFactory [0;39m [2m:[0;39m DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@642f9a77
[2m2025-07-30 17:20:38.693[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-30 17:20:38.694[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-30 17:20:38.752[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-30 17:20:38.752[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-30 17:20:39.077[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.web.DefaultSecurityFilterChain    [0;39m [2m:[0;39m Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4416e18d, org.springframework.security.web.context.SecurityContextPersistenceFilter@51eb0e84, org.springframework.security.web.header.HeaderWriterFilter@1c68d0db, org.springframework.security.web.authentication.logout.LogoutFilter@28cb86b2, org.springframework.security.web.authentication.www.BasicAuthenticationFilter@3b96f8b0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2eebce87, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@13d289c7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5dfc2a4, org.springframework.security.web.session.SessionManagementFilter@5fb7ab9c, org.springframework.security.web.access.ExceptionTranslationFilter@4438938e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5c4f4330]
[2m2025-07-30 17:20:39.084[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m No URLs will be polled as dynamic configuration sources.
[2m2025-07-30 17:20:39.084[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.c.sources.URLConfigurationSource    [0;39m [2m:[0;39m To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
[2m2025-07-30 17:20:39.160[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService 'applicationTaskExecutor'
[2m2025-07-30 17:20:39.630[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mockingLoadBalancerClientRibbonWarnLogger[0;39m [2m:[0;39m You already have RibbonLoadBalancerClient on your classpath. It will be used by default. As Spring Cloud Ribbon is in maintenance mode. We recommend switching to BlockingLoadBalancerClient instead. In order to use it, set the value of `spring.cloud.loadbalancer.ribbon.enabled` to `false` or remove spring-cloud-starter-netflix-ribbon from your project.
[2m2025-07-30 17:20:39.678[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-30 17:20:39.691[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Initializing Eureka in region us-east-1
[2m2025-07-30 17:20:39.691[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Client configured to neither register nor query for data.
[2m2025-07-30 17:20:39.695[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.netflix.discovery.DiscoveryClient   [0;39m [2m:[0;39m Discovery Client initialized at timestamp 1753867239694 with initial instances count: 0
[2m2025-07-30 17:20:39.713[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initializing ...
[2m2025-07-30 17:20:39.715[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Adding new peer nodes [**********************************************/eureka/]
[2m2025-07-30 17:20:39.999[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON encoding codec LegacyJacksonJson
[2m2025-07-30 17:20:40.000[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using JSON decoding codec LegacyJacksonJson
[2m2025-07-30 17:20:40.000[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML encoding codec XStreamXml
[2m2025-07-30 17:20:40.000[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.d.provider.DiscoveryJerseyProvider  [0;39m [2m:[0;39m Using XML decoding codec XStreamXml
[2m2025-07-30 17:20:40.049[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.cluster.PeerEurekaNodes      [0;39m [2m:[0;39m Replica node URL:  **********************************************/eureka/
[2m2025-07-30 17:20:40.055[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Finished initializing remote region registries. All known remote regions: []
[2m2025-07-30 17:20:40.056[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.n.eureka.DefaultEurekaServerContext   [0;39m [2m:[0;39m Initialized
[2m2025-07-30 17:20:40.062[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 2 endpoint(s) beneath base path '/actuator'
[2m2025-07-30 17:20:40.104[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application SS-PLATFORM-EUREKA with eureka with status UP
[2m2025-07-30 17:20:40.105[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Setting the eureka configuration..
[2m2025-07-30 17:20:40.106[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka data center value eureka.datacenter is not set, defaulting to default
[2m2025-07-30 17:20:40.106[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Eureka environment value eureka.environment is not set, defaulting to test
[2m2025-07-30 17:20:40.111[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m isAws returned false
[2m2025-07-30 17:20:40.112[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mo.s.c.n.e.server.EurekaServerBootstrap  [0;39m [2m:[0;39m Initialized server context
[2m2025-07-30 17:20:40.112[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Got 1 instances from neighboring DS node
[2m2025-07-30 17:20:40.112[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Renew threshold is: 1
[2m2025-07-30 17:20:40.112[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Changing status to UP
[2m2025-07-30 17:20:40.117[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[      Thread-19][0;39m [36me.s.EurekaServerInitializerConfiguration[0;39m [2m:[0;39m Started Eureka Server
[2m2025-07-30 17:20:40.129[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 7602 (http) with context path ''
[2m2025-07-30 17:20:40.130[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 7602
[2m2025-07-30 17:20:40.411[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.p.eureka.EurekaServerApplication    [0;39m [2m:[0;39m Started EurekaServerApplication in 4.384 seconds (JVM running for 5.999)
[2m2025-07-30 17:20:41.037[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-30 17:20:41.042[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[1)-192.168.1.49][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 5 ms
[2m2025-07-30 17:21:04.196[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=false)
[2m2025-07-30 17:21:04.834[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-SCHEDULE/DESKTOP-658MVB3:ss-platform-schedule:7606 with status UP (replication=true)
[2m2025-07-30 17:21:10.863[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-3][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=false)
[2m2025-07-30 17:21:11.389[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-6][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-GATEWAY/DESKTOP-658MVB3:ss-platform-gateway:7601 with status UP (replication=true)
[2m2025-07-30 17:21:12.954[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=false)
[2m2025-07-30 17:21:13.476[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-AUTHZTH/DESKTOP-658MVB3:ss-platform-authzth:7604 with status UP (replication=true)
[2m2025-07-30 17:21:14.444[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=false)
[2m2025-07-30 17:21:14.976[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-PLATFORM-COMMON/DESKTOP-658MVB3:ss-platform-common:7612 with status UP (replication=true)
[2m2025-07-30 17:21:40.115[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 17:22:06.341[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 17:22:06.861[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 17:22:40.127[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 17:23:40.141[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 17:24:40.147[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:25:40.148[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 17:26:40.153[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:27:40.166[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 17:28:40.173[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 17:29:40.181[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 17:30:40.192[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 17:31:40.206[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 17:32:08.674[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-8][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-30 17:32:09.201[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=true)
[2m2025-07-30 17:32:40.216[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 17:33:40.219[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 17:33:40.219[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Evicting 1 items (expired=1, evictionLimit=1)
[2m2025-07-30 17:33:40.220[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: expired lease for SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-30 17:33:40.224[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-30 17:33:44.270[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 17:33:44.791[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-2][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 17:34:40.233[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 13ms
[2m2025-07-30 17:35:40.073[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 17:35:40.237[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 17:36:40.242[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:37:40.254[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 17:38:07.001[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-30 17:38:07.529[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=true)
[2m2025-07-30 17:38:40.257[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 2ms
[2m2025-07-30 17:39:40.266[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 17:39:40.266[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Evicting 1 items (expired=1, evictionLimit=1)
[2m2025-07-30 17:39:40.266[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: expired lease for SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-30 17:39:40.266[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-30 17:39:52.724[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-1][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 17:39:53.252[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-5][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 17:40:40.275[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 17:41:40.275[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 17:42:40.281[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 6ms
[2m2025-07-30 17:43:40.294[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 17:44:40.304[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 17:45:40.309[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:46:40.310[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 17:47:40.325[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 17:48:40.336[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 17:48:41.739[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[io-7602-exec-10][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=false)
[2m2025-07-30 17:48:41.950[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status DOWN (replication=true)
[2m2025-07-30 17:49:40.343[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 17:50:40.076[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 17:50:40.353[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 17:50:40.353[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Evicting 1 items (expired=1, evictionLimit=1)
[2m2025-07-30 17:50:40.353[0;39m [dev] [33m WARN[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m DS: Registry: expired lease for SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608
[2m2025-07-30 17:50:40.354[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Cancelled instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 (replication=false)
[2m2025-07-30 17:51:23.223[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-4][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=false)
[2m2025-07-30 17:51:23.742[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[nio-7602-exec-9][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Registered instance SS-IFRS-ACTUARIAL/DESKTOP-658MVB3:ss-ifrs-actuarial:7608 with status UP (replication=true)
[2m2025-07-30 17:51:40.362[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 9ms
[2m2025-07-30 17:52:40.373[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 17:53:40.400[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 26ms
[2m2025-07-30 17:54:40.403[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 17:55:40.409[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 17:56:40.423[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 17:57:40.439[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 15ms
[2m2025-07-30 17:58:40.447[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 17:59:40.455[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 8ms
[2m2025-07-30 18:00:40.456[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 18:01:40.471[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 14ms
[2m2025-07-30 18:02:40.481[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 10ms
[2m2025-07-30 18:03:40.481[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 0ms
[2m2025-07-30 18:04:40.488[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 18:05:40.080[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[hresholdUpdater][0;39m [36mc.n.e.r.PeerAwareInstanceRegistryImpl   [0;39m [2m:[0;39m Current renewal threshold is : 0
[2m2025-07-30 18:05:40.492[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 3ms
[2m2025-07-30 18:06:40.503[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 11ms
[2m2025-07-30 18:07:40.511[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 7ms
[2m2025-07-30 18:08:40.517[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 5ms
[2m2025-07-30 18:09:40.530[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 18:10:40.542[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 12ms
[2m2025-07-30 18:11:40.547[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
[2m2025-07-30 18:12:40.548[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 1ms
[2m2025-07-30 18:13:40.553[0;39m [dev] [32m INFO[0;39m [35m34992[0;39m [2m---[0;39m [2m[a-EvictionTimer][0;39m [36mc.n.e.registry.AbstractInstanceRegistry [0;39m [2m:[0;39m Running the evict task with compensationTime 4ms
