package com.ss.library.mybatis.handler;

import com.alibaba.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;

import java.lang.ref.WeakReference;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * EasyExcel资源管理器
 * 用于管理ExcelWriter资源，防止内存泄漏
 * 
 * <AUTHOR>
 */
@Slf4j
public class ExcelResourceManager {
    
    private static final ConcurrentHashMap<String, WeakReference<ExcelWriter>> writerRegistry = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "ExcelResourceCleanup");
        t.setDaemon(true);
        return t;
    });
    
    static {
        // 启动定期清理任务，每5分钟执行一次
        cleanupExecutor.scheduleAtFixedRate(ExcelResourceManager::cleanupExpiredWriters, 5, 5, TimeUnit.MINUTES);
    }
    
    /**
     * 注册ExcelWriter
     * 
     * @param writerId Writer标识
     * @param writer ExcelWriter实例
     */
    public static void registerWriter(String writerId, ExcelWriter writer) {
        if (writerId != null && writer != null) {
            writerRegistry.put(writerId, new WeakReference<>(writer));
            log.debug("注册ExcelWriter: {}", writerId);
        }
    }
    
    /**
     * 安全关闭ExcelWriter
     * 
     * @param writerId Writer标识
     */
    public static void safeCloseWriter(String writerId) {
        if (writerId == null) {
            return;
        }
        
        WeakReference<ExcelWriter> writerRef = writerRegistry.remove(writerId);
        if (writerRef != null) {
            ExcelWriter writer = writerRef.get();
            if (writer != null) {
                try {
                    writer.finish();
                    log.debug("成功关闭ExcelWriter: {}", writerId);
                } catch (Exception e) {
                    log.error("关闭ExcelWriter时发生异常: {}", writerId, e);
                }
            }
        }
    }
    
    /**
     * 安全关闭ExcelWriter（直接传入Writer实例）
     * 
     * @param writer ExcelWriter实例
     * @param operationName 操作名称（用于日志）
     */
    public static void safeCloseWriter(ExcelWriter writer, String operationName) {
        if (writer == null) {
            return;
        }
        
        try {
            writer.finish();
            log.debug("成功关闭ExcelWriter: {}", operationName);
        } catch (Exception e) {
            log.error("关闭ExcelWriter时发生异常: {}", operationName, e);
        }
    }
    
    /**
     * 清理已过期的Writer引用
     */
    private static void cleanupExpiredWriters() {
        try {
            int initialSize = writerRegistry.size();
            writerRegistry.entrySet().removeIf(entry -> {
                WeakReference<ExcelWriter> writerRef = entry.getValue();
                return writerRef.get() == null;
            });
            
            int finalSize = writerRegistry.size();
            if (initialSize > finalSize) {
                log.debug("清理了 {} 个过期的ExcelWriter引用", initialSize - finalSize);
            }
        } catch (Exception e) {
            log.error("清理过期ExcelWriter引用时发生异常", e);
        }
    }
    
    /**
     * 获取当前注册的Writer数量
     * 
     * @return Writer数量
     */
    public static int getRegisteredWriterCount() {
        return writerRegistry.size();
    }
    
    /**
     * 强制清理所有注册的Writer
     */
    public static void forceCleanupAllWriters() {
        try {
            log.info("开始强制清理所有ExcelWriter，当前数量: {}", writerRegistry.size());
            
            writerRegistry.forEach((writerId, writerRef) -> {
                ExcelWriter writer = writerRef.get();
                if (writer != null) {
                    try {
                        writer.finish();
                        log.debug("强制关闭ExcelWriter: {}", writerId);
                    } catch (Exception e) {
                        log.error("强制关闭ExcelWriter时发生异常: {}", writerId, e);
                    }
                }
            });
            
            writerRegistry.clear();
            log.info("完成强制清理所有ExcelWriter");
            
        } catch (Exception e) {
            log.error("强制清理ExcelWriter时发生异常", e);
        }
    }
    
    /**
     * 关闭资源管理器
     */
    public static void shutdown() {
        try {
            forceCleanupAllWriters();
            cleanupExecutor.shutdown();
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
            log.info("ExcelResourceManager已关闭");
        } catch (Exception e) {
            log.error("关闭ExcelResourceManager时发生异常", e);
        }
    }
    
    /**
     * 创建Writer标识
     * 
     * @param fileName 文件名
     * @param threadName 线程名
     * @return Writer标识
     */
    public static String createWriterId(String fileName, String threadName) {
        return String.format("%s_%s_%d", fileName, threadName, System.currentTimeMillis());
    }
}
