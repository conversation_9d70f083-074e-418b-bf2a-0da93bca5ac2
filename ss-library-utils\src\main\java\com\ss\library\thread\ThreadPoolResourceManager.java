package com.ss.library.thread;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池资源管理工具类
 * 用于监控和管理线程池资源，防止内存泄漏
 * 
 * <AUTHOR>
 */
@Slf4j
public class ThreadPoolResourceManager {
    
    private static final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
    
    /**
     * 监控线程池状态
     * 
     * @param executor 线程池执行器
     * @param poolName 线程池名称
     */
    public static void monitorThreadPool(ThreadPoolTaskExecutor executor, String poolName) {
        if (executor == null) {
            return;
        }
        
        ThreadPoolExecutor threadPoolExecutor = executor.getThreadPoolExecutor();
        if (threadPoolExecutor == null) {
            return;
        }
        
        log.info("线程池状态监控 [{}] - 核心线程数: {}, 最大线程数: {}, 当前线程数: {}, 活跃线程数: {}, 队列大小: {}, 已完成任务数: {}",
                poolName,
                threadPoolExecutor.getCorePoolSize(),
                threadPoolExecutor.getMaximumPoolSize(),
                threadPoolExecutor.getPoolSize(),
                threadPoolExecutor.getActiveCount(),
                threadPoolExecutor.getQueue().size(),
                threadPoolExecutor.getCompletedTaskCount());
    }
    
    /**
     * 监控内存使用情况
     * 
     * @param operationName 操作名称
     */
    public static void monitorMemoryUsage(String operationName) {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemoryUsage = memoryMXBean.getNonHeapMemoryUsage();
        
        long heapUsed = heapMemoryUsage.getUsed();
        long heapMax = heapMemoryUsage.getMax();
        long nonHeapUsed = nonHeapMemoryUsage.getUsed();
        
        double heapUsagePercent = (double) heapUsed / heapMax * 100;
        
        log.info("内存使用监控 [{}] - 堆内存使用: {}MB/{}MB ({}%), 非堆内存使用: {}MB",
                operationName,
                heapUsed / 1024 / 1024,
                heapMax / 1024 / 1024,
                String.format("%.2f", heapUsagePercent),
                nonHeapUsed / 1024 / 1024);
        
        // 如果内存使用率超过80%，记录警告
        if (heapUsagePercent > 80) {
            log.warn("内存使用率过高 [{}] - 当前使用率: {}%, 建议检查是否存在内存泄漏", 
                    operationName, String.format("%.2f", heapUsagePercent));
        }
    }
    
    /**
     * 执行资源清理
     * 
     * @param operationName 操作名称
     */
    public static void performResourceCleanup(String operationName) {
        try {
            // 记录清理前的内存状态
            monitorMemoryUsage(operationName + " - 清理前");
            
            // 建议JVM进行垃圾回收
            System.gc();
            
            // 等待一小段时间让GC完成
            Thread.sleep(100);
            
            // 记录清理后的内存状态
            monitorMemoryUsage(operationName + " - 清理后");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("资源清理过程被中断: {}", operationName, e);
        } catch (Exception e) {
            log.error("执行资源清理时发生异常: {}", operationName, e);
        }
    }
    
    /**
     * 检查是否需要进行资源清理
     * 
     * @return 如果内存使用率超过阈值则返回true
     */
    public static boolean shouldPerformCleanup() {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        long heapUsed = heapMemoryUsage.getUsed();
        long heapMax = heapMemoryUsage.getMax();
        double heapUsagePercent = (double) heapUsed / heapMax * 100;
        
        // 如果内存使用率超过75%，建议进行清理
        return heapUsagePercent > 75;
    }
    
    /**
     * 安全关闭线程池
     * 
     * @param executor 线程池执行器
     * @param poolName 线程池名称
     * @param timeoutSeconds 等待超时时间（秒）
     */
    public static void safeShutdownThreadPool(ThreadPoolTaskExecutor executor, String poolName, int timeoutSeconds) {
        if (executor == null) {
            return;
        }
        
        try {
            log.info("开始关闭线程池: {}", poolName);
            
            // 设置等待任务完成
            executor.setWaitForTasksToCompleteOnShutdown(true);
            executor.setAwaitTerminationSeconds(timeoutSeconds);
            
            // 关闭线程池
            executor.shutdown();
            
            log.info("线程池关闭完成: {}", poolName);
            
        } catch (Exception e) {
            log.error("关闭线程池时发生异常: {}", poolName, e);
        }
    }
}
